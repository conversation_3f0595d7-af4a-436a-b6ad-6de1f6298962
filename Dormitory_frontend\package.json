{"name": "demo", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@element-plus/icons-vue": "^0.2.7", "axios": "^0.25.0", "core-js": "^3.6.5", "echarts": "^5.3.0", "element-plus": "^2.0.1", "vue": "^3.0.0", "vue-axios": "^3.4.1", "vue-router": "^4.0.13", "vuex": "^4.0.0-0", "wangeditor": "^4.7.11"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "element": "^0.1.4"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}