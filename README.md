### 学生宿舍管理系统

#### 1、课设项目介绍

该系统拥有三种角色：

:older_man:系统管理员：

* 查看当前宿舍学生人数、住宿人数、报修数量、空舍数量、查看学生信息、宿管信息、查看楼宇信息、查看公告信息、查看房间信息、查看报修信息、查看调寝信息、访客管理、查看所有用户信息。

:woman:宿舍管理员：

* 查看当前宿舍学生人数、住宿人数、报修数量、空舍数量、查看学生信息、查看楼宇信息、查看公告信息、查看房间信息、查看报修信息、查看调寝信息、访客管理、查看个人信息。

:baby:学生：

* 查看当前宿舍学生人数、住宿人数、报修数量、空舍数量、查看我的宿舍、申请调宿、申请报修、查看个人信息。

#### 2、开发工具

​	后端：IDEA   
​	前端：VS Code 或 WebStorm

#### 3、项目技术

​	后端框架：Springboot、MyBatis、Mybatis Plus
​	前端技术：ElementUI、vue、css、JavaScript、axios

#### 4、环境：

JDK>=1.8、Maven、Node.js >=16、MySQL >=8.0

#### 5、硬件环境：

Windows 或者 Mac OS(不挑剔)

### 配置

sql 文件在 doc 中

1. 后端文件夹 `Dormitory_backend` ：

	修改 `application.properties` 文件

	![image](doc/img/application.properties文件.png)

	将端口号和数据库地址、账号、密码改为自己的

	启动 `SpringbootApplication `

3. 前端文件夹 `Dormitory_frontend` ：

	进入文件夹目录后打开控制台cmd

	输入 `npm install` 安装前端环境，而后 `npm run serve` 启动前端

4. 用户名或密码在数据库中，查看三种用户的三个表


# 后端核心代码文件的作用及相互联系

## 1. 项目架构概述

学生宿舍管理系统后端采用了经典的三层架构设计，包括：

1. **控制层（Controller）**：处理HTTP请求，接收前端数据，调用服务层处理业务逻辑，返回处理结果
2. **服务层（Service）**：实现业务逻辑，处理数据，调用数据访问层操作数据库
3. **数据访问层（Mapper/DAO）**：与数据库交互，执行SQL语句，实现数据的增删改查

此外，还包括实体类（Entity）、通用工具类（Common）和应用入口类（SpringbootApplication）。

## 2. 核心代码文件及其作用

### 2.1 应用入口

**SpringbootApplication.java**

- 作用：应用程序的入口点，启动Spring Boot应用
- 包含`@SpringBootApplication`注解，标识这是一个Spring Boot应用
- 包含`@MapperScan`注解，指定MyBatis Mapper接口的扫描路径



Copy



@SpringBootApplication

@MapperScan("com/example/springboot/mapper")

public class SpringbootApplication {

public static void main(String[] args) {

​    SpringApplication.run(SpringbootApplication.class, args);

}

}

### 2.2 实体类（Entity）

实体类对应数据库表结构，用于在应用中表示数据模型。

**Student.java**

- 作用：表示学生实体，对应数据库中的学生表
- 包含学生的基本信息：学号、姓名、性别、年龄、联系方式等

**DormManager.java**

- 作用：表示宿舍管理员实体，对应数据库中的宿管表
- 包含宿管的基本信息：工号、姓名、性别、年龄、联系方式等

**Admin.java**

- 作用：表示系统管理员实体，对应数据库中的管理员表
- 包含管理员的基本信息：ID、用户名、密码等

**DormBuild.java**

- 作用：表示宿舍楼宇实体，对应数据库中的楼宇表
- 包含楼宇信息：楼宇ID、名称、楼层数、管理员等

**DormRoom.java**

- 作用：表示宿舍房间实体，对应数据库中的房间表
- 包含房间信息：房间ID、所属楼宇、房间号、床位数、当前入住人数等
- 包含床位信息：各床位上的学生信息

**Repair.java**

- 作用：表示报修申请实体，对应数据库中的报修表
- 包含报修信息：报修ID、报修学生、报修内容、报修时间、报修状态等

**AdjustRoom.java**

- 作用：表示调宿申请实体，对应数据库中的调宿表
- 包含调宿信息：申请ID、申请学生、当前宿舍、目标宿舍、申请时间、申请状态等

**Notice.java**

- 作用：表示公告实体，对应数据库中的公告表
- 包含公告信息：公告ID、标题、内容、发布时间、发布人等

**Visitor.java**

- 作用：表示访客记录实体，对应数据库中的访客表
- 包含访客信息：访客ID、姓名、性别、联系方式、来源地、来访时间、来访事由等

### 2.3 控制层（Controller）

控制层负责处理HTTP请求，是前端与后端交互的接口。

**MainController.java**

- 作用：处理用户身份验证和会话管理相关的请求
- 提供获取用户身份、获取用户信息和退出登录等接口



Copy



@RestController

@RequestMapping("/main")

public class MainController {

@GetMapping("/loadIdentity")

public Result<?> loadIdentity(HttpSession session) {

​    Object identity = session.getAttribute("Identity");

​    if (identity != null) {

​      return Result.success(identity);

​    } else {

​      return Result.error("-1", "加载失败");

​    }

}



// 其他方法...

}

**AdminController.java**

- 作用：处理管理员相关的请求
- 提供管理员登录、信息管理等接口

**DormManagerController.java**

- 作用：处理宿舍管理员相关的请求
- 提供宿管添加、信息更新等接口

**StudentController.java**

- 作用：处理学生相关的请求
- 提供学生登录、信息管理、统计查询等接口

**DormBuildController.java**

- 作用：处理楼宇相关的请求
- 提供楼宇的增删改查接口

**DormRoomController.java**

- 作用：处理房间相关的请求
- 提供房间的增删改查、床位管理、空宿舍统计等接口

**RepairController.java**

- 作用：处理报修相关的请求
- 提供报修申请提交、查询、处理等接口

**AdjustRoomController.java**

- 作用：处理调宿相关的请求
- 提供调宿申请提交、查询、审核等接口

**NoticeController.java**

- 作用：处理公告相关的请求
- 提供公告发布、查询、修改、删除等接口

**VisitorController.java**

- 作用：处理访客相关的请求
- 提供访客登记、查询等接口

**FileController.java**

- 作用：处理文件上传相关的请求
- 提供头像上传、获取等接口

### 2.4 服务层（Service）

服务层实现业务逻辑，是控制层和数据访问层之间的桥梁。

**服务接口**：

- `AdminService.java`
- `DormManagerService.java`
- `StudentService.java`
- `DormBuildService.java`
- `DormRoomService.java`
- `RepairService.java`
- `AdjustRoomService.java`
- `NoticeService.java`
- `VisitorService.java`

**服务实现类**：

- `AdminServiceImpl.java`
- `DormManagerServiceImpl.java`
- `StudentServiceImpl.java`
- `DormBuildImpl.java`
- `DormRoomImpl.java`
- `RepairServiceImpl.java`
- `AdjustRoomServiceImpl.java`
- `NoticeServiceImpl.java`
- `VisitorServiceImpl.java`

以

```
 DormRoomService.java
```

为例：





Copy



public interface DormRoomService extends IService<DormRoom> {

//统计没有住满的宿舍数量

public int notFullRoom();

//新增房间

int addNewRoom(DormRoom dormRoom);

//查询房间

Page find(Integer pageNum, Integer pageSize, String search);

//更新房间信息

int updateNewRoom(DormRoom dormRoom);

//删除房间信息

int deleteRoom(Integer dormRoomId);



// 其他方法...

}

### 2.5 数据访问层（Mapper）

数据访问层负责与数据库交互，执行SQL语句。

**Mapper接口**：

- `AdminMapper.java`
- `DormManagerMapper.java`
- `StudentMapper.java`
- `DormBuildMapper.java`
- `DormRoomMapper.java`
- `RepairMapper.java`
- `AdjustRoomMapper.java`
- `NoticeMapper.java`
- `VisitorMapper.java`

### 2.6 通用工具类（Common）

**Result.java**

- 作用：统一API返回结果格式
- 提供成功和错误响应的静态方法



Copy



public class Result<T> {

private String code;

private String msg;

private T data;



public static <T> Result<T> success(T data) {

​    Result<T> result = new Result<>();

​    result.setCode("0");

​    result.setData(data);

​    return result;

}



public static <T> Result<T> error(String code, String msg) {

​    Result<T> result = new Result<>();

​    result.setCode(code);

​    result.setMsg(msg);

​    return result;

}



// getter和setter方法...

}

**CorsConfig.java**

- 作用：配置跨域资源共享（CORS）
- 允许前端从不同域名访问后端API

**MyBatisPlusConfig.java**

- 作用：配置MyBatis Plus
- 设置分页插件等

**UID.java**、**JudgeBedName.java**、**CalPeopleNum.java**

- 作用：提供特定功能的工具类
- 如生成唯一ID、判断床位名称、计算人数等

## 3. 文件之间的联系

### 3.1 层级之间的联系

1. 控制层与服务层

   ：

	- 控制层通过依赖注入（`@Resource`或`@Autowired`）引用服务层接口
	- 控制层接收HTTP请求，调用服务层方法处理业务逻辑，返回处理结果

2. 服务层与数据访问层

   ：

	- 服务层通过依赖注入引用数据访问层接口
	- 服务层实现业务逻辑，调用数据访问层方法操作数据库

3. 实体类与各层的联系

   ：

	- 实体类在各层之间传递数据
	- 控制层接收前端数据，转换为实体类对象
	- 服务层处理实体类对象，实现业务逻辑
	- 数据访问层将实体类对象转换为数据库记录，或将数据库记录转换为实体类对象

### 3.2 具体示例：学生登录流程

1. 前端发送登录请求到`StudentController`的`login`方法
2. `StudentController`接收用户名和密码，调用`StudentService`的`stuLogin`方法
3. `StudentServiceImpl`实现`stuLogin`方法，调用`StudentMapper`查询数据库
4. `StudentMapper`执行SQL语句，查询学生信息
5. 查询结果返回给`StudentServiceImpl`，进行密码验证
6. 验证结果返回给`StudentController`
7. `StudentController`根据验证结果，使用`Result`类生成统一格式的响应
8. 响应返回给前端



Copy



// StudentController.java

@PostMapping("/login")

public Result<?> login(@RequestBody User user, HttpSession session) {

Object o = studentService.stuLogin(user.getUsername(), user.getPassword());

if (o != null) {

​    //存入session

​    session.setAttribute("Identity", "stu");

​    session.setAttribute("User", o);

​    return Result.success(o);

} else {

​    return Result.error("-1", "用户名或密码错误");

}

}

// StudentServiceImpl.java

@Override

public Object stuLogin(String username, String password) {

// 调用Mapper查询数据库

Student student = studentMapper.selectByUsername(username);

if (student != null && password.equals(student.getPassword())) {

​    return student;

}

return null;

}

### 3.3 功能模块之间的联系

1. 用户认证与其他模块

   ：

	- 用户登录后，身份信息存储在Session中
	- 其他模块通过Session获取用户身份，实现权限控制

2. 宿舍管理与学生管理

   ：

	- 学生入住宿舍时，需要更新宿舍房间的入住信息
	- 查询学生信息时，可能需要关联查询其宿舍信息

3. 调宿管理与宿舍管理

   ：

	- 调宿申请审核通过后，需要更新学生的宿舍信息和相关房间的入住信息
	- 查询可调宿的目标宿舍时，需要查询房间的空床位情况

4. 报修管理与宿舍管理

   ：

	- 提交报修申请时，需要关联学生的宿舍信息
	- 查询报修记录时，可能需要按宿舍楼宇或房间进行筛选

## 4. 总结

学生宿舍管理系统后端采用了清晰的三层架构设计，各层次职责明确，相互协作完成系统功能。核心代码文件按照功能模块和架构层次组织，通过依赖注入实现层与层之间的解耦。系统各功能模块之间存在数据关联和业务流程上的联系，共同构成了一个完整的宿舍管理解决方案。

这种架构设计使得系统具有良好的可维护性和可扩展性，便于后续功能的添加和修改。同时，统一的返回结果格式和异常处理机制，提高了系统的稳定性和用户体验。


