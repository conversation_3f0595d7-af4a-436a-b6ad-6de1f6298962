<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.springboot.SpringbootApplicationTests" time="4.947" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="20"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business\target\test-classes;D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business\target\classes;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-web\2.6.3\spring-boot-starter-web-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter\2.6.3\spring-boot-starter-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-logging\2.6.3\spring-boot-starter-logging-2.6.3.jar;D:\Java\Maven\repository\ch\qos\logback\logback-classic\1.2.10\logback-classic-1.2.10.jar;D:\Java\Maven\repository\ch\qos\logback\logback-core\1.2.10\logback-core-1.2.10.jar;D:\Java\Maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.1\log4j-to-slf4j-2.17.1.jar;D:\Java\Maven\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;D:\Java\Maven\repository\org\slf4j\jul-to-slf4j\1.7.33\jul-to-slf4j-1.7.33.jar;D:\Java\Maven\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\Java\Maven\repository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-json\2.6.3\spring-boot-starter-json-2.6.3.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-databind\2.13.1\jackson-databind-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.1\jackson-annotations-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-core\2.13.1\jackson-core-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.1\jackson-datatype-jdk8-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.1\jackson-datatype-jsr310-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.1\jackson-module-parameter-names-2.13.1.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-tomcat\2.6.3\spring-boot-starter-tomcat-2.6.3.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.56\tomcat-embed-core-9.0.56.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.56\tomcat-embed-el-9.0.56.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.56\tomcat-embed-websocket-9.0.56.jar;D:\Java\Maven\repository\org\springframework\spring-web\5.3.15\spring-web-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-beans\5.3.15\spring-beans-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-webmvc\5.3.15\spring-webmvc-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-aop\5.3.15\spring-aop-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-context\5.3.15\spring-context-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-expression\5.3.15\spring-expression-5.3.15.jar;D:\Java\Maven\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.2\mybatis-spring-boot-starter-2.2.2.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-jdbc\2.6.3\spring-boot-starter-jdbc-2.6.3.jar;D:\Java\Maven\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\Java\Maven\repository\org\springframework\spring-jdbc\5.3.15\spring-jdbc-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-tx\5.3.15\spring-tx-5.3.15.jar;D:\Java\Maven\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.2\mybatis-spring-boot-autoconfigure-2.2.2.jar;D:\Java\Maven\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;D:\Java\Maven\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-devtools\2.6.3\spring-boot-devtools-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot\2.6.3\spring-boot-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-autoconfigure\2.6.3\spring-boot-autoconfigure-2.6.3.jar;D:\Java\Maven\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;D:\Java\Maven\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-test\2.6.3\spring-boot-starter-test-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-test\2.6.3\spring-boot-test-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.3\spring-boot-test-autoconfigure-2.6.3.jar;D:\Java\Maven\repository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;D:\Java\Maven\repository\net\minidev\json-smart\2.4.7\json-smart-2.4.7.jar;D:\Java\Maven\repository\net\minidev\accessors-smart\2.4.7\accessors-smart-2.4.7.jar;D:\Java\Maven\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\Java\Maven\repository\org\slf4j\slf4j-api\1.7.33\slf4j-api-1.7.33.jar;D:\Java\Maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\Java\Maven\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\Java\Maven\repository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;D:\Java\Maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\Java\Maven\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Java\Maven\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\Java\Maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\Java\Maven\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\Java\Maven\repository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;D:\Java\Maven\repository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;D:\Java\Maven\repository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;D:\Java\Maven\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Java\Maven\repository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;D:\Java\Maven\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\Java\Maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Java\Maven\repository\org\springframework\spring-core\5.3.15\spring-core-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-jcl\5.3.15\spring-jcl-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-test\5.3.15\spring-test-5.3.15.jar;D:\Java\Maven\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\Java\Maven\repository\com\github\jsqlparser\jsqlparser\4.3\jsqlparser-4.3.jar;D:\Java\Maven\repository\commons-beanutils\commons-beanutils\1.9.3\commons-beanutils-1.9.3.jar;D:\Java\Maven\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;D:\Java\Maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\Java\Maven\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;D:\Java\Maven\repository\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar;D:\Java\Maven\repository\net\sf\json-lib\json-lib\2.2.3\json-lib-2.2.3-jdk15.jar;D:\Java\Maven\repository\com\alibaba\fastjson\1.2.28\fastjson-1.2.28.jar;D:\Java\Maven\repository\cn\hutool\hutool-all\5.7.3\hutool-all-5.7.3.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 10"/>
    <property name="java.vm.specification.version" value="20"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Java\JDK 20.0.1\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire7185657379367299349\surefirebooter17035443260552333877.jar C:\Users\<USER>\AppData\Local\Temp\surefire7185657379367299349 2024-01-02T14-23-33_172-jvmRun1 surefire1169169625144060390tmp surefire_03121172595884957705tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business\target\test-classes;D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business\target\classes;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-web\2.6.3\spring-boot-starter-web-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter\2.6.3\spring-boot-starter-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-logging\2.6.3\spring-boot-starter-logging-2.6.3.jar;D:\Java\Maven\repository\ch\qos\logback\logback-classic\1.2.10\logback-classic-1.2.10.jar;D:\Java\Maven\repository\ch\qos\logback\logback-core\1.2.10\logback-core-1.2.10.jar;D:\Java\Maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.17.1\log4j-to-slf4j-2.17.1.jar;D:\Java\Maven\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;D:\Java\Maven\repository\org\slf4j\jul-to-slf4j\1.7.33\jul-to-slf4j-1.7.33.jar;D:\Java\Maven\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\Java\Maven\repository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-json\2.6.3\spring-boot-starter-json-2.6.3.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-databind\2.13.1\jackson-databind-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.1\jackson-annotations-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\core\jackson-core\2.13.1\jackson-core-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.1\jackson-datatype-jdk8-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.1\jackson-datatype-jsr310-2.13.1.jar;D:\Java\Maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.1\jackson-module-parameter-names-2.13.1.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-tomcat\2.6.3\spring-boot-starter-tomcat-2.6.3.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.56\tomcat-embed-core-9.0.56.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.56\tomcat-embed-el-9.0.56.jar;D:\Java\Maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.56\tomcat-embed-websocket-9.0.56.jar;D:\Java\Maven\repository\org\springframework\spring-web\5.3.15\spring-web-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-beans\5.3.15\spring-beans-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-webmvc\5.3.15\spring-webmvc-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-aop\5.3.15\spring-aop-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-context\5.3.15\spring-context-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-expression\5.3.15\spring-expression-5.3.15.jar;D:\Java\Maven\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.2\mybatis-spring-boot-starter-2.2.2.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-jdbc\2.6.3\spring-boot-starter-jdbc-2.6.3.jar;D:\Java\Maven\repository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;D:\Java\Maven\repository\org\springframework\spring-jdbc\5.3.15\spring-jdbc-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-tx\5.3.15\spring-tx-5.3.15.jar;D:\Java\Maven\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.2\mybatis-spring-boot-autoconfigure-2.2.2.jar;D:\Java\Maven\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;D:\Java\Maven\repository\org\mybatis\mybatis-spring\2.0.7\mybatis-spring-2.0.7.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-devtools\2.6.3\spring-boot-devtools-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot\2.6.3\spring-boot-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-autoconfigure\2.6.3\spring-boot-autoconfigure-2.6.3.jar;D:\Java\Maven\repository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;D:\Java\Maven\repository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-starter-test\2.6.3\spring-boot-starter-test-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-test\2.6.3\spring-boot-test-2.6.3.jar;D:\Java\Maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.3\spring-boot-test-autoconfigure-2.6.3.jar;D:\Java\Maven\repository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;D:\Java\Maven\repository\net\minidev\json-smart\2.4.7\json-smart-2.4.7.jar;D:\Java\Maven\repository\net\minidev\accessors-smart\2.4.7\accessors-smart-2.4.7.jar;D:\Java\Maven\repository\org\ow2\asm\asm\9.1\asm-9.1.jar;D:\Java\Maven\repository\org\slf4j\slf4j-api\1.7.33\slf4j-api-1.7.33.jar;D:\Java\Maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\Java\Maven\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\Java\Maven\repository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;D:\Java\Maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;D:\Java\Maven\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\Java\Maven\repository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;D:\Java\Maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;D:\Java\Maven\repository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;D:\Java\Maven\repository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;D:\Java\Maven\repository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;D:\Java\Maven\repository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;D:\Java\Maven\repository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;D:\Java\Maven\repository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;D:\Java\Maven\repository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;D:\Java\Maven\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\Java\Maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Java\Maven\repository\org\springframework\spring-core\5.3.15\spring-core-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-jcl\5.3.15\spring-jcl-5.3.15.jar;D:\Java\Maven\repository\org\springframework\spring-test\5.3.15\spring-test-5.3.15.jar;D:\Java\Maven\repository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\Java\Maven\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\Java\Maven\repository\com\github\jsqlparser\jsqlparser\4.3\jsqlparser-4.3.jar;D:\Java\Maven\repository\commons-beanutils\commons-beanutils\1.9.3\commons-beanutils-1.9.3.jar;D:\Java\Maven\repository\commons-collections\commons-collections\3.2.1\commons-collections-3.2.1.jar;D:\Java\Maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\Java\Maven\repository\commons-logging\commons-logging\1.1.1\commons-logging-1.1.1.jar;D:\Java\Maven\repository\net\sf\ezmorph\ezmorph\1.0.6\ezmorph-1.0.6.jar;D:\Java\Maven\repository\net\sf\json-lib\json-lib\2.2.3\json-lib-2.2.3-jdk15.jar;D:\Java\Maven\repository\com\alibaba\fastjson\1.2.28\fastjson-1.2.28.jar;D:\Java\Maven\repository\cn\hutool\hutool-all\5.7.3\hutool-all-5.7.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\JDK 20.0.1"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire7185657379367299349\surefirebooter17035443260552333877.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="20.0.1*****"/>
    <property name="user.name" value="16937"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="D:\Java\Maven\repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2022.1.3"/>
    <property name="java.version" value="20.0.1"/>
    <property name="user.dir" value="D:\Java Web\大作业\DormitoryManagementSystem\Dormitory_business"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Java\JDK 20.0.1\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\Java\jdk1.8.0_361\bin;D:\Java\jdk1.8.0_361\jre\bin;C:\Program Files\Microsoft\jdk-***********-hotspot\bin;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;D:\IDE\Eclipse Adoptium\jdk-*********-hotspot\bin;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Users\<USER>\AppData\Roaming\Python\Python39\Scripts;C:\Users\<USER>\AppData\Roaming\pip\pip.ini;D:\CodeBlocks\MinGW\bin;D:\VSCode\Microsoft VS Code\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\微信web开发者工具\dll;&quot;%JAVA;HOME%\jre\bin;D:\Java\Maven\apache-maven-3.6.3\jre\bin;&quot;;D:\Git\Git\cmd;D:\IDE\LLVM\bin;D:\Java\JDK 20.0.1\bin;D:\Java\JDK 20.0.1\jre\bin;D:\Tomcat\apache-tomcat-10.1.13\bin;D:\Java\Maven\apache-maven-3.6.3\bin;D:\Develop\Node.js\node_global\node_modules;D:\MySQL\MySQL Server 8.0\bin;D:\MySQL\apache-tomcat-8.5.90\bin;D:\Tomcat\apache-tomcat-10.1.13\bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;D:\HuiBian\MASM 6.x\;D:\Develop\Node.js\;D:\Develop\Node.js\node_global;D:\Python\Python39\Scripts\;D:\Python\Python39\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Heroku CLI\heroku\bin;D:\VSCode\Microsoft VS Code\bin;D:\Python\pycharm\PyCharm Community Edition 2021.3.2\bin;;D:\VSCode\MinGW\bin;;D:\IDE\IntelliJ IDEA 2022.1.3\bin;;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Roaming\npm;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="20.0.1*****"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="64.0"/>
  </properties>
  <testcase name="contextLoads" classname="com.example.springboot.SpringbootApplicationTests" time="0.348"/>
</testsuite>