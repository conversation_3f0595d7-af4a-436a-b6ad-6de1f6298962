package com.example.springboot.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 作用：表示报修申请实体，对应数据库中的报修表
 * 包含报修信息：报修ID、报修学生、报修内容、报修时间、报修状态等
 */
@Data
@AllArgsConstructor
@NoArgsConstructor

@TableName(value = "repair")
public class Repair {

    @TableId(value = "id")
    private Integer id;
    @TableField("repairer")
    private String repairer;
    @TableField("dormbuild_id")
    private int dormBuildId;
    @TableField("dormroom_id")
    private int dormRoomId;
    @TableField("title")
    private String title;
    @TableField("content")
    private String content;
    @TableField("state")
    private String state;
    @TableField("order_buildtime")
    private String orderBuildTime;
    @TableField("order_finishtime")
    private String orderFinishTime;
}
